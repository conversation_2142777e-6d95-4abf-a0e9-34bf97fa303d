import { Platform } from "react-native"

export const BASE_URL = Platform.OS==='android' ? 'http://********:3000/api': 'http://localhost:3000/api'
export const SOCKET_URL =  Platform.OS==='android' ? 'http://********:3000': 'http://localhost:3000'
export const GOOGLE_MAP_API = "AIzaSyDOBBimUu_eGMwsXZUqrNFk3puT5rMWbig"
export const BRANCH_ID ='YOUR_BRANCH_ID'

// USE YOUR NETWORK IP OR HOSTED URL
// export const BASE_URL = 'http://***********:3000/api'
// export const SOCKET_URL = 'http://***********:3000'

